### 1. 重置云服务器密码

*   **目的**：为了方便后续操作，需要将云服务器的密码重置为常用密码。
*   **操作步骤**：
    *   登录华为云管理控制台。
    *   在云服务器列表中找到目标服务器。
    *   点击“重置密码”。
    *   按照提示设置新的密码。

### 2. 登录Windows云服务器

*   **目的**：通过远程桌面连接（MSTSC）方式登录云服务器，进行后续操作。
*   **操作步骤**：
    *   在Windows系统中打开“远程桌面连接”。
    *   输入云服务器的IP地址和端口。
    *   输入新设置的密码。
    *   点击“连接”。

### 3. 安装git

*   **目的**：用于将Node.js项目从本地代码库同步到云服务器。
*   **操作步骤**：
    *   在云服务器中打开命令提示符。
    *   输入以下命令安装git：
        ```bash
        npm install -g git
        ```

### 4. 安装Workbench

*   **目的**：Workbench是华为云提供的代码编辑器，可以方便地进行代码编辑和调试。
*   **操作步骤**：
    *   在华为云管理控制台中搜索“Workbench”。
    *   点击“立即使用”。
    *   按照提示完成安装。

### 5. 安装Node.js

*   **目的**：Node.js是运行Node.js应用程序的平台。
*   **操作步骤**：
    *   访问Node.js官网（https://nodejs.cn/）。
    *   下载适合Windows系统的Node.js安装包。
    *   运行安装包并按照提示完成安装。

### 6. 安装pm2

*   **目的**：pm2是Node.js应用程序的进程管理器，可以方便地进行进程管理、监控和日志记录。
*   **操作步骤**：
    *   在云服务器中打开命令提示符。
    *   输入以下命令安装pm2：
        ```bash
        npm install -g pm2
        ```